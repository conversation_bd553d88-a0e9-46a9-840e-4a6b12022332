'use client';
import { Flex, Box, Image, Link, Stack, Tabs, TabList, Tab } from '@chakra-ui/react';
import IMAGES from '@/assets/images';
import useScreenDetector from '@/utils/use-screen-detector';
import { useRouter, usePathname } from 'next/navigation';
import UserAuthButton from '../atoms/UserAuthButton';
import useUserPropaga from '@/utils/use-user-propaga';
import { Roles } from '@/constants';

const Header = () => {
  const { isMobile } = useScreenDetector();
  const router = useRouter();
  const pathname = usePathname();
  const { role } = useUserPropaga();

  const handleTabChange = (index: number) => {
    if (index === 0) {
      router.push('/dashboard');
    } else {
      router.push('/dashboard/reconciliation');
    }
  };

  const currentTab = pathname === '/dashboard/reconciliation' ? 1 : 0;

  const getTabs = () => {
    return (
      <Tabs
        variant="soft-rounded"
        colorScheme="gray"
        size="sm"
        index={currentTab}
        onChange={handleTabChange}
      >
        {role === Roles.ADMIN && (
          <TabList>
            <Tab>Conciliación</Tab>
            <Tab>Liquidación</Tab>
          </TabList>
        )}
      </Tabs>
    );
  };

  return (
    <>
      {!isMobile ? (
        <Flex
          as="header"
          align="center"
          justify="space-between"
          p={4}
          bg="gray.200"
          shadow="md"
          pl={8}
          pr={8}
          w={'100%'}
        >
          <Box>
            <Stack direction={'row'} spacing={8} align="center">
              <Link href="/dashboard">
                <Image alt="propaga" w={5} h={5} src={IMAGES.PropagaLogo} align={'center'} />
              </Link>
              {getTabs()}
            </Stack>
          </Box>
          <UserAuthButton />
        </Flex>
      ) : (
        <Flex
          as="header"
          align="center"
          justify="space-between"
          bg="gray.200"
          py={2}
          px={4}
          direction="column"
        >
          <Flex w="100%" justify="space-between" align="center" mb={2}>
            <Box>
              <Link href="/dashboard">
                <Image alt="propaga" w={5} h={5} src={IMAGES.PropagaLogo} align={'center'} />
              </Link>
              {getTabs()}
            </Box>
            <UserAuthButton />
          </Flex>
        </Flex>
      )}
    </>
  );
};

export default Header;
