'use client';

import PropagaButton from '@/components/atoms/Button';
import SuccessIcon from '@/components/icons/SuccessIcon';
import Loading from '@/components/molecules/Loading';
import ReferenceCard from '@/components/molecules/ReferenceCard';
import TransactionSummary from '@/components/organisms/TransactionSummary';
import { useServices } from '@/services';
import COLORS from '@/styles/colors';
import { Flex, Heading, Stack, Text, useToast } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';

const Page = () => {
  const router = useRouter();
  const { transactionId }: { transactionId: string } = useParams();
  const { transactionService } = useServices();
  const toast = useToast();

  const { data: transaction, isLoading } = useQuery({
    queryFn: () => transactionService.getTransaction(transactionId),
    queryKey: ['getTransactionById', transactionId],
  });

  if (isLoading) {
    return <Loading />;
  }

  const handleCopyReference = () => {
    navigator.clipboard.writeText(transaction.wholesalerTransactionId);
    toast({
      title: 'Referencia copiada al portapapeles',
      status: 'success',
      position: 'top',
    });
  };

  return (
    <Flex h={'100%'}>
      <Flex w={'50%'} h={'100vh'} py={12} px={40} alignItems="center" justifyContent="center">
        <Stack spacing={12} w={'100%'}>
          <Stack spacing={4} textAlign="center">
            <SuccessIcon />
            <Heading size={'lg'}>Orden completada</Heading>
          </Stack>
          <ReferenceCard
            referenceNumber={transaction.wholesalerTransactionId}
            onClick={handleCopyReference}
          />
          <Stack spacing={4}>
            <Text textColor={COLORS.DarkGray} textAlign={'center'}>
              Imprimiendo recibo...
            </Text>
            <PropagaButton
              autoFocus
              onClick={() => {
                handleCopyReference();
                router.push('/transaction');
              }}
            >
              Continuar
            </PropagaButton>
          </Stack>
        </Stack>
      </Flex>
      <Flex w={'50%'} h={'100vh'} py={20} px={12}>
        <TransactionSummary transaction={transaction} />
      </Flex>
    </Flex>
  );
};

export default Page;
